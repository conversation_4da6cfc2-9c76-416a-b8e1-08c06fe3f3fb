import {
	findStandar<PERSON><PERSON>ieldMapping,
	getStandardFieldMappings,
	type StandardFieldMapping,
} from "@/config/standardFieldMappings";
import type { GetCCCustomField } from "@/type/CCTypes";
import matchString from "@/utils/matchString";

/**
 * AutoPatient field types as defined in the DATA-TYPE-MAP.md
 */
type APFieldType =
	| "TEXT"
	| "LARGE_TEXT"
	| "NUMERICAL"
	| "PHONE"
	| "MONETORY"
	| "CHECKBOX"
	| "SINGLE_OPTIONS"
	| "MULTIPLE_OPTIONS"
	| "DATE"
	| "RADIO"
	| "EMAIL"
	| "TEXTBOX_LIST"
	| "FILE_UPLOAD";

/**
 * CliniCore field types as defined in the DATA-TYPE-MAP.md
 */
type CCFieldType =
	| "text"
	| "textarea"
	| "number"
	| "telephone"
	| "email"
	| "date"
	| "select"
	| "select-or-custom"
	| "boolean"
	| "medication"
	| "permanent-diagnoses"
	| "patient-has-recommended";

/**
 * Enhanced field mapping result that can represent either standard field mappings or custom field type conversions
 */
interface EnhancedFieldMappingResult {
	/** Type of mapping performed */
	mappingType: "standard_field" | "custom_field";

	/** Standard field mapping information (when mappingType is "standard_field") */
	standardFieldMapping?: {
		/** Target field name on the destination platform */
		targetField: string;
		/** Source platform where this is a custom field */
		sourcePlatform: "cc" | "ap";
		/** Target platform where this maps to a standard field */
		targetPlatform: "cc" | "ap";
		/** Optional mapping notes */
		notes?: string;
	};

	/** Custom field type (when mappingType is "custom_field") */
	customFieldType?: APFieldType;

	/** Confidence score for the mapping (0-1) */
	confidence: number;

	/** Original field name used for mapping */
	fieldName?: string;

	/** Original field label used for mapping */
	fieldLabel?: string;
}

/**
 * Result of AP to CC field type conversion
 */
interface APToCCConversionResult {
	type: CCFieldType | null;
	allowMultipleValues: boolean;
}


/**
 * Converts a CliniCore (CC) custom field type to the corresponding AutoPatient (AP) field type.
 * Enhanced version can also detect standard field mappings when context is provided.
 *
 * This function implements the field type mapping rules defined in DATA-TYPE-MAP.md:
 *
 * **Key Rules:**
 * 1. CC fields with `allowMultipleValues=true` map to AP `TEXTBOX_LIST` regardless of base type,
 *    EXCEPT for CC `select` fields which prefer `MULTIPLE_OPTIONS` for semantic accuracy
 * 2. Unknown CC field types default to AP `TEXT` (or `TEXTBOX_LIST` if `allowMultipleValues=true`)
 * 3. Medical field types (`medication`, `permanent-diagnoses`, `patient-has-recommended`) fallback to `TEXT`
 * 4. CC `boolean` fields map to AP `RADIO` with Yes/Ja, No/Nein options
 *
 * **Multi-Value Handling Priority:**
 * - CC `select` with `allowMultipleValues=true` → AP `MULTIPLE_OPTIONS` (preferred for semantic accuracy)
 * - All other CC field types with `allowMultipleValues=true` → AP `TEXTBOX_LIST`
 *
 * **Standard Field Detection (Enhanced Mode):**
 * When context is provided, the function can detect standard field mappings:
 * - CC custom fields with names like "phone", "email" → AP standard fields
 * - AP standard fields like "phone", "email" → CC custom fields
 * - Uses STANDARD_FIELD_MAPPINGS configuration for detection
 * - Returns structured result indicating mapping type
 *
 * @param ccField - CliniCore custom field object containing type, allowMultipleValues, and optionally name/label
 * @param context - Optional context for standard field detection with smart defaults
 * @returns APFieldType (basic mode) or EnhancedFieldMappingResult (enhanced mode)
 *
 * @example
 * ```typescript
 * // Basic field type mapping (backward compatible)
 * const textField = { type: "text", allowMultipleValues: false };
 * mapCCFieldToAPType(textField); // Returns "TEXT"
 *
 * // Enhanced mode with auto-extracted field info (RECOMMENDED)
 * const ccField = { type: "telephone", allowMultipleValues: false, name: "phone", label: "Phone Number" };
 * const result = mapCCFieldToAPType(ccField, {});
 * // Automatically uses ccField.name and ccField.label, defaults to CC→AP direction
 * // Returns: { mappingType: "standard_field", standardFieldMapping: { targetField: "phone", ... } }
 *
 * // Enhanced mode with custom field conversion
 * const customField = { type: "text", allowMultipleValues: false, name: "customNote", label: "Custom Note" };
 * const result2 = mapCCFieldToAPType(customField, {});
 * // Returns: { mappingType: "custom_field", customFieldType: "TEXT", ... }
 *
 * // Reverse direction: AP standard field to CC custom field
 * const emailField = { type: "email", allowMultipleValues: false, name: "email", label: "Email" };
 * const result3 = mapCCFieldToAPType(emailField, {
 *   sourcePlatform: "ap",
 *   targetPlatform: "cc"
 * });
 * // Returns: { mappingType: "standard_field", standardFieldMapping: { targetField: "email", ... } }
 *
 * // Override field name/label for custom matching
 * const phoneField = { type: "telephone", allowMultipleValues: false, name: "mobile", label: "Mobile" };
 * const result4 = mapCCFieldToAPType(phoneField, {
 *   fieldLabel: "Phone Mobile"  // Override label for fuzzy matching
 * });
 * // Returns: { mappingType: "standard_field", standardFieldMapping: { targetField: "phone", ... } }
 * ```
 */
// Function overloads for backward compatibility and enhanced functionality
function mapCCFieldToAPType(
	ccField: Pick<GetCCCustomField, "type" | "allowMultipleValues">,
): APFieldType;
function mapCCFieldToAPType(
	ccField: Pick<GetCCCustomField, "type" | "allowMultipleValues" | "name" | "label">,
	context: {
		fieldName?: string;
		fieldLabel?: string;
		sourcePlatform?: "cc" | "ap";
		targetPlatform?: "cc" | "ap";
	},
): EnhancedFieldMappingResult;
function mapCCFieldToAPType(
	ccField: Pick<GetCCCustomField, "type" | "allowMultipleValues"> | Pick<GetCCCustomField, "type" | "allowMultipleValues" | "name" | "label">,
	context?: {
		fieldName?: string;
		fieldLabel?: string;
		sourcePlatform?: "cc" | "ap";
		targetPlatform?: "cc" | "ap";
	},
): APFieldType | EnhancedFieldMappingResult {
	// If no context provided, use original logic for backward compatibility
	if (!context) {
		return mapCCFieldToAPTypeBasic(ccField);
	}

	// Enhanced mode with standard field detection
	return mapCCFieldToAPTypeEnhanced(ccField as Pick<GetCCCustomField, "type" | "allowMultipleValues" | "name" | "label">, context);
}

/**
 * Original field type mapping logic (for backward compatibility)
 */
function mapCCFieldToAPTypeBasic(
	ccField: Pick<GetCCCustomField, "type" | "allowMultipleValues">,
): APFieldType {
	const { type, allowMultipleValues } = ccField;

	// Handle multi-value fields first (priority-based system)
	if (allowMultipleValues) {
		// CC select fields with allowMultipleValues → AP MULTIPLE_OPTIONS (preferred for semantic accuracy)
		if (type === "select" || type === "select-or-custom") {
			return "MULTIPLE_OPTIONS";
		}

		// All other CC field types with allowMultipleValues → AP TEXTBOX_LIST
		return "TEXTBOX_LIST";
	}

	// Handle single-value field mappings based on DATA-TYPE-MAP.md
	switch (type) {
		// Basic field type mappings
		case "text":
			return "TEXT";
		case "textarea":
			return "LARGE_TEXT";
		case "number":
			return "NUMERICAL";
		case "telephone":
			return "PHONE";
		case "email":
			return "EMAIL";
		case "date":
			return "DATE";

		// Select field mappings
		case "select":
		case "select-or-custom":
			return "SINGLE_OPTIONS";

		// Boolean field mapping (creates RADIO with Yes/Ja, No/Nein options)
		case "boolean":
			return "RADIO";

		// Medical field fallbacks (as defined in DATA-TYPE-MAP.md)
		case "medication":
		case "permanent-diagnoses":
		case "patient-has-recommended":
			return "TEXT";

		// Unknown field type fallback
		default:
			console.warn(`Unknown CC field type "${type}", defaulting to TEXT`);
			return allowMultipleValues ? "TEXTBOX_LIST" : "TEXT";
	}
}

/**
 * Enhanced field type mapping with standard field detection
 */
function mapCCFieldToAPTypeEnhanced(
	ccField: Pick<GetCCCustomField, "type" | "allowMultipleValues" | "name" | "label">,
	context: {
		fieldName?: string;
		fieldLabel?: string;
		sourcePlatform?: "cc" | "ap";
		targetPlatform?: "cc" | "ap";
	},
): EnhancedFieldMappingResult {
	// Extract field info with smart defaults
	const fieldName = context.fieldName ?? (ccField as any).name;
	const fieldLabel = context.fieldLabel ?? (ccField as any).label;
	const sourcePlatform = context.sourcePlatform ?? "cc";
	const targetPlatform = context.targetPlatform ?? "ap";

	// Try to find standard field mapping using field name first, then label
	let standardMapping: StandardFieldMapping | null = null;
	let matchedFieldName: string | undefined;
	let confidence = 0;

	if (fieldName) {
		standardMapping = findStandardFieldMapping(
			fieldName,
			sourcePlatform,
			targetPlatform,
		);
		if (standardMapping) {
			matchedFieldName = fieldName;
			confidence = 1.0; // Exact name match
		}
	}

	// If no exact name match, try label
	if (!standardMapping && fieldLabel) {
		standardMapping = findStandardFieldMapping(
			fieldLabel,
			sourcePlatform,
			targetPlatform,
		);
		if (standardMapping) {
			matchedFieldName = fieldLabel;
			confidence = 0.9; // Label match (slightly lower confidence)
		}
	}

	// If no exact matches, try fuzzy matching with matchString utility
	if (!standardMapping && (fieldName || fieldLabel)) {
		const testName = fieldName || fieldLabel;

		// Get all standard field mappings for this platform direction
		const allMappings = getStandardFieldMappings(
			sourcePlatform,
			targetPlatform,
		);

		for (const mapping of allMappings) {
			if (matchString(testName, mapping.sourceField)) {
				standardMapping = mapping;
				matchedFieldName = testName;
				confidence = 0.8; // Fuzzy match (lower confidence)
				break;
			}
		}
	}

	// If standard field mapping found, return standard field result
	if (standardMapping) {
		return {
			mappingType: "standard_field",
			standardFieldMapping: {
				targetField: standardMapping.targetField,
				sourcePlatform: standardMapping.sourcePlatform,
				targetPlatform: standardMapping.targetPlatform,
				notes: standardMapping.notes,
			},
			confidence,
			fieldName: matchedFieldName,
			fieldLabel,
		};
	}

	// No standard field mapping found, return custom field type conversion
	const customFieldType = mapCCFieldToAPTypeBasic(ccField);

	return {
		mappingType: "custom_field",
		customFieldType,
		confidence: 1.0, // High confidence for type conversion
		fieldName,
		fieldLabel,
	};
}





/**
 * Converts an AutoPatient (AP) field type to the corresponding CliniCore (CC) field type.
 *
 * This function implements the reverse field type mapping rules defined in DATA-TYPE-MAP.md:
 *
 * **Key Rules:**
 * 1. AP `TEXTBOX_LIST` maps to CC `text` with `allowMultipleValues=true`
 * 2. AP `MULTIPLE_OPTIONS` and `CHECKBOX` map to CC `select` with `allowMultipleValues=true`
 * 3. AP `SINGLE_OPTIONS` and `RADIO` map to CC `select` with `allowMultipleValues=false`
 * 4. AP `RADIO` with Yes/No options can optionally map to CC `boolean` (context-dependent)
 * 5. AP `MONETORY` maps to CC `text` (monetary values stored as text in CC)
 * 6. AP `FILE_UPLOAD` is skipped (not supported in CC)
 *
 * **Multi-Value Handling:**
 * - AP fields that handle multiple values (`TEXTBOX_LIST`, `MULTIPLE_OPTIONS`, `CHECKBOX`)
 *   map to CC fields with `allowMultipleValues=true`
 * - Single-value AP fields map to CC fields with `allowMultipleValues=false`
 *
 * @param apFieldType - AutoPatient field type to convert
 * @param options - Optional conversion options
 * @param options.preferBoolean - If true, RADIO fields with Yes/No values prefer CC boolean over select
 * @returns Conversion result with CC field type, allowMultipleValues flag, and optional notes
 *
 * @example
 * ```typescript
 * // Basic field type mapping
 * mapAPFieldToCCType("TEXT");
 * // Returns { type: "text", allowMultipleValues: false }
 *
 * // Multi-value field mapping
 * mapAPFieldToCCType("TEXTBOX_LIST");
 * // Returns { type: "text", allowMultipleValues: true }
 *
 * // Select field mappings
 * mapAPFieldToCCType("MULTIPLE_OPTIONS");
 * // Returns { type: "select", allowMultipleValues: true }
 *
 * mapAPFieldToCCType("SINGLE_OPTIONS");
 * // Returns { type: "select", allowMultipleValues: false }
 *
 * // Radio field with boolean preference
 * mapAPFieldToCCType("RADIO", { preferBoolean: true });
 * // Returns { type: "boolean", allowMultipleValues: false, notes: "..." }
 *
 * // Monetary field mapping
 * mapAPFieldToCCType("MONETORY");
 * // Returns { type: "text", allowMultipleValues: false, notes: "..." }
 * ```
 */
function mapAPFieldToCCType(
	apFieldType: APFieldType,
): APToCCConversionResult {
	switch (apFieldType) {
		// Basic field type mappings
		case "TEXT":
			return { type: "text", allowMultipleValues: false };

		case "LARGE_TEXT":
			return { type: "textarea", allowMultipleValues: false };

		case "NUMERICAL":
			return { type: "number", allowMultipleValues: false };

		case "PHONE":
			return { type: "telephone", allowMultipleValues: false };

		case "EMAIL":
			return { type: "email", allowMultipleValues: false };

		case "DATE":
			return { type: "date", allowMultipleValues: false };

		// Multi-value field mappings
		case "TEXTBOX_LIST":
			return {
				type: "text",
				allowMultipleValues: true,
			};

		// Select field mappings
		case "SINGLE_OPTIONS":
			return { type: "select", allowMultipleValues: false };

		case "MULTIPLE_OPTIONS":
			return {
				type: "select",
				allowMultipleValues: true,
			};

		case "CHECKBOX":
			return {
				type: "select",
				allowMultipleValues: false,
			};

		// Radio field mapping (context-dependent)
		case "RADIO":
			return {
				type: "boolean",
				allowMultipleValues: false,
			};

		// Special field mappings
		case "MONETORY":
			return {
				type: "number",
				allowMultipleValues: false,
			};
		// Unknown field type fallback
		default:
			// Log unknown field type for debugging
			console.warn(
				`Unknown AP field type "${apFieldType}", defaulting to text`,
			);
			return {
				type: null,
				allowMultipleValues: false,
			};
	}
}

/**
 * Utility function to check if an AP field type supports multiple values.
 *
 * @param apFieldType - AutoPatient field type to check
 * @returns True if the field type supports multiple values
 *
 * @example
 * ```typescript
 * isAPFieldMultiValue("TEXTBOX_LIST"); // Returns true
 * isAPFieldMultiValue("MULTIPLE_OPTIONS"); // Returns true
 * isAPFieldMultiValue("TEXT"); // Returns false
 * ```
 */

/**
 * Utility function to check if a CC field supports multiple values based on its configuration.
 *
 * @param ccField - CliniCore custom field object
 * @returns True if the field supports multiple values
 *
 * @example
 * ```typescript
 * const field = { type: "text", allowMultipleValues: true };
 * isCCFieldMultiValue(field); // Returns true
 * ```
 */

/**
 * Utility function to get both conversion directions for a field type.
 * Useful for validation and compatibility checking.
 *
 * @param ccField - CliniCore custom field object
 * @returns Object containing both conversion directions
 *
 * @example
 * ```typescript
 * const ccField = { type: "text", allowMultipleValues: true };
 * const conversions = getBidirectionalMapping(ccField);
 * // Returns:
 * // {
 * //   ccToAP: "TEXTBOX_LIST",
 * //   apToCC: { type: "text", allowMultipleValues: true, notes: "..." }
 * // }
 * ```
 */

/**
 * Simple function to map CC field to AP with enhanced field detection.
 * No extra parameters needed - automatically extracts field info and uses CC→AP direction.
 *
 * @param ccField - CliniCore custom field object
 * @returns Enhanced mapping result with standard field detection
 *
 * @example
 * ```typescript
 * const result = mapCCtoAPField(ccField);
 * if (result.mappingType === "standard_field") {
 *   console.log(`Maps to AP standard field: ${result.standardFieldMapping?.targetField}`);
 * } else {
 *   console.log(`Create AP custom field of type: ${result.customFieldType}`);
 * }
 * ```
 */
export function mapCCtoAPField(ccField: GetCCCustomField): EnhancedFieldMappingResult {
	return mapCCFieldToAPType(ccField, {}) as EnhancedFieldMappingResult;
}

/**
 * Simple function to map AP field to CC with enhanced field detection.
 * No extra parameters needed - automatically extracts field info and uses AP→CC direction.
 *
 * @param apField - AutoPatient field object with name and type info
 * @returns Enhanced mapping result with standard field detection
 *
 * @example
 * ```typescript
 * const apField = { name: "email", type: "email", allowMultipleValues: false };
 * const result = mapAPtoCCField(apField);
 * if (result.mappingType === "standard_field") {
 *   console.log(`Maps to CC standard field: ${result.standardFieldMapping?.targetField}`);
 * } else {
 *   console.log(`Create CC custom field of type: ${result.customFieldConversion?.type}`);
 * }
 * ```
 */
