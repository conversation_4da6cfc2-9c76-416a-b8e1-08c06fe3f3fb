import apiClient from "@/apiClient";
import { executeMappingPlan, generateMappingPlan } from "./fieldMappingService";
/**
 * Custom Fields Processor
 *
 * Simple processor that returns empty JSON response.
 */

export async function synchronizeCustomFields(): Promise<{}> {
  const apCustomFields = await apiClient.ap.apCustomfield.allWithParentFilter();
  const ccCustomFields = await apiClient.cc.ccCustomfieldReq.all();

  const { decisions, summary } = generateMappingPlan(
    ccCustomFields,
    apCustomFields
  );
  const execution = await executeMappingPlan(decisions);
  return {
    summary,
    execution,
  };
}
