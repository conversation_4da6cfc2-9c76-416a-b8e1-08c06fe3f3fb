import apiClient from "@/apiClient";
import { logDebug, logWarn, logError, logCustomField } from "@/utils/logger";
import type { APGetCustomFieldType, GetCCPatientCustomField } from "@/type";

// Supported output value types for AP contact customFields[].value/field_value
export type APConvertedValue =
  | string
  | number
  | string[]
  | Record<string, string>
  | null;

// Helper: Extract string values from CC custom field structure
function extractStringsFromCCValue(ccField: GetCCPatientCustomField): string[] {
  if (!ccField?.values || !Array.isArray(ccField.values)) return [];

  const out: string[] = [];
  const allowedValues = ccField.field?.allowedValues || [];

  for (const valueItem of ccField.values) {
    if (valueItem == null) continue;

    // If value has a direct string value, use it
    if (valueItem.value !== undefined && valueItem.value !== null) {
      const s = String(valueItem.value).trim();
      if (s !== "") out.push(s);
      continue;
    }

    // If value has an ID, try to resolve it to a label using allowedValues
    if (valueItem.id !== undefined && valueItem.id !== null && allowedValues.length > 0) {
      const allowedValue = allowedValues.find(av => av.id === valueItem.id);
      if (allowedValue && allowedValue.value) {
        const s = String(allowedValue.value).trim();
        if (s !== "") out.push(s);
        continue;
      }
    }
  }

  return out;
}

// Legacy helper for backward compatibility with unknown input
function extractStringsFromUnknown(input: unknown): string[] {
  if (input == null) return [];

  // Already array of primitives
  if (Array.isArray(input)) {
    const out: string[] = [];
    for (const v of input) {
      if (v == null) continue;
      if (typeof v === "string" || typeof v === "number") {
        const s = String(v).trim();
        if (s !== "") out.push(s);
        continue;
      }
      if (typeof v === "object") {
        // CC often uses { value?: string } or { id?: number }
        const maybeValue = (v as { value?: unknown }).value;
        if (maybeValue !== undefined && maybeValue !== null) {
          const s = String(maybeValue).trim();
          if (s !== "") out.push(s);
          continue;
        }
        // If only an id is present, we cannot resolve to label without CC field meta
        // Return empty for this item (caller will decide fallback/skip)
      }
    }
    return out;
  }

  // Single primitive
  if (typeof input === "string" || typeof input === "number") {
    const s = String(input).trim();
    return s === "" ? [] : [s];
  }

  // Single object { value }
  if (typeof input === "object") {
    const maybeValue = (input as { value?: unknown }).value;
    if (maybeValue !== undefined && maybeValue !== null) {
      const s = String(maybeValue).trim();
      return s === "" ? [] : [s];
    }
  }

  return [];
}

// Helper: parse number safely
function toNumber(value: unknown): number | null {
  if (value == null) return null;
  const num = typeof value === "number" ? value : Number(String(value).trim());
  return Number.isFinite(num) ? num : null;
}

// Helper: get picklist labels (and ids if available)
function getPicklist(
  field: APGetCustomFieldType,
): { id?: string; label: string }[] {
  const opts = field.picklistOptions;
  if (!opts) return [];
  if (Array.isArray(opts)) {
    if (opts.length === 0) return [];
    const first = opts[0] as unknown;
    // Structured with ids
    if (
      typeof first === "object" &&
      first !== null &&
      "label" in (first as Record<string, unknown>)
    ) {
      return (opts as Array<{ id: string; label: string; prefillValue?: string }>)
        .map(o => ({ id: o.id, label: String(o.label) }));
    }
    // Simple string labels
    return (opts as string[]).map(label => ({ label }));
  }
  return [];
}

// Helper: get textbox list option ids if exposed via picklistOptions; fallback to index-based mapping
function getTextboxOptionIds(
  field: APGetCustomFieldType,
): string[] {
  const picklist = getPicklist(field);
  if (picklist.length > 0 && picklist.every(p => typeof p.label === "string")) {
    const withIds = picklist.filter(p => p.id);
    if (withIds.length === picklist.length) {
      return withIds.map(p => p.id as string);
    }
  }
  // Fallback: derive count from textBoxListOptions to at least maintain slot count
  const count = Array.isArray(field.textBoxListOptions)
    ? field.textBoxListOptions.length
    : 0;
  // We cannot fabricate real IDs; return empty to signal missing ids
  if (count > 0) {
    logWarn("TEXTBOX_LIST missing picklist option ids; cannot build field_value keys deterministically", {
      fieldId: field.id,
      fieldName: field.name,
      count,
    });
  }
  return [];
}

// Normalize and filter multi values for EMAIL/PHONE rules: drop empty strings entirely
function filterNonEmpty(values: string[]): string[] {
  return values.filter(v => v.trim() !== "");
}

/**
 * Convert a CC custom field value into AP-compatible value for the given AP custom field ID.
 *
 * - Fetches AP custom field config (to determine dataType and options)
 * - Applies value conversion rules consistent with DATA-TYPE-MAP.md and repo preferences
 * - Handles multi-value (TEXTBOX_LIST, MULTIPLE_OPTIONS/CHECKBOX) and option mapping
 * - Filters useless values (empty strings) and gracefully handles mismatches
 */
export async function convertCCToAPValue(
  ccValue: GetCCPatientCustomField,
  apFieldId: string,
): Promise<APConvertedValue> {
  if (!apFieldId || apFieldId.trim() === "") {
    logWarn("convertCCToAPValue called without apFieldId");
    return null;
  }

  try {
    const field = await apiClient.ap.apCustomfield.get(apFieldId, true);
    const dataType = (field.dataType || "").toUpperCase();

    logCustomField("Value conversion - start", field.name, {
      apFieldId,
      dataType,
      ccFieldId: ccValue.field?.id,
      ccFieldType: ccValue.field?.type,
      ccAllowMultipleValues: ccValue.field?.allowMultipleValues,
      ccValuesCount: ccValue.values?.length || 0,
    });

    switch (dataType) {
      case "TEXT":
      case "LARGE_TEXT": {
        const parts = extractStringsFromCCValue(ccValue);
        if (parts.length === 0) return null;
        // If multiple values arrive for TEXT, join with " | " per rules
        const value = parts.length === 1 ? parts[0] : parts.join(" | ");
        return value;
      }

      case "EMAIL": {
        const parts = filterNonEmpty(extractStringsFromCCValue(ccValue));
        if (parts.length === 0) return null;
        return parts[0];
      }

      case "PHONE": {
        const parts = filterNonEmpty(extractStringsFromCCValue(ccValue));
        if (parts.length === 0) return null;
        return parts[0];
      }

      case "DATE": {
        const parts = extractStringsFromCCValue(ccValue);
        if (parts.length === 0) return null;
        // Pass-through; upstream should ensure format if needed
        return parts[0];
      }

      case "NUMERICAL":
      case "MONETORY": {
        // Try to get numeric value from first CC value
        const parts = extractStringsFromCCValue(ccValue);
        if (parts.length === 0) return null;
        const num = toNumber(parts[0]);
        if (num !== null) return num;
        // Fallback to string if parsed number unavailable
        return parts[0];
      }

      case "FILE_UPLOAD": {
        // CC→AP for files is out of scope; skip
        logWarn("Skipping FILE_UPLOAD conversion for CC→AP", { apFieldId });
        return null;
      }

      case "RADIO":
      case "SINGLE_OPTIONS": {
        const picklist = getPicklist(field);
        const parts = extractStringsFromCCValue(ccValue);
        if (parts.length === 0) return null;
        const candidate = parts[0];

        if (picklist.length === 0) {
          // If field allows custom option, pass through
          if (field.isAllowedCustomOption) return candidate;
          logWarn("Options field has no picklistOptions; cannot validate value", {
            apFieldId,
            fieldName: field.name,
            candidate,
          });
          return candidate;
        }

        // Match by case-insensitive label
        const match = picklist.find(
          (o) => o.label.toLowerCase() === candidate.toLowerCase(),
        );
        if (match) return match.label;

        if (field.isAllowedCustomOption) {
          logDebug("Value not in picklist, allowed custom option", {
            apFieldId,
            candidate,
          });
          return candidate;
        }

        logWarn("Skipping options value not present in picklist", {
          apFieldId,
          candidate,
          picklistPreview: picklist.slice(0, 5).map(p => p.label),
        });
        return null;
      }

      case "MULTIPLE_OPTIONS":
      case "CHECKBOX": {
        const picklist = getPicklist(field);
        const parts = extractStringsFromCCValue(ccValue);
        if (parts.length === 0) return null;

        if (picklist.length === 0) {
          // Cannot validate; if custom options allowed, pass through all
          if (field.isAllowedCustomOption) return parts;
          logWarn("Multi-options field has no picklistOptions; cannot validate values", {
            apFieldId,
            fieldName: field.name,
          });
          return parts;
        }

        // Keep only values present in picklist (case-insensitive)
        const pickset = new Map<string, string>();
        for (const p of picklist) pickset.set(p.label.toLowerCase(), p.label);
        const matched: string[] = [];
        for (const v of parts) {
          const label = pickset.get(v.toLowerCase());
          if (label) matched.push(label);
          else if (field.isAllowedCustomOption) matched.push(v);
          else {
            logWarn("Dropping invalid MULTIPLE_OPTIONS value (not in picklist)", {
              apFieldId,
              value: v,
            });
          }
        }
        return matched.length > 0 ? matched : null;
      }

      case "TEXTBOX_LIST": {
        // Expect to return field_value: Record<optionId, string>
        const values = extractStringsFromCCValue(ccValue);
        const optionIds = getTextboxOptionIds(field);
        if (values.length === 0) return {};

        if (optionIds.length === 0) {
          // We cannot construct proper object keys without option IDs
          logWarn("Cannot construct TEXTBOX_LIST field_value (missing option ids)", {
            apFieldId,
            valuesCount: values.length,
          });
          // Fallback: return joined text to allow at least storing somewhere when caller targets TEXT
          // But since this function targets a specific AP field (which is TEXTBOX_LIST), safest is to skip
          // and let caller decide. Return null to avoid malformed payloads.
          return null;
        }

        // Map values to first N option ids
        const field_value: Record<string, string> = {};
        const max = Math.min(values.length, optionIds.length);
        for (let i = 0; i < max; i++) {
          const val = values[i];
          if (val.trim() === "") continue;
          field_value[optionIds[i]] = val;
        }
        // Complete replacement semantics: return only filled keys
        return field_value;
      }

      default: {
        logWarn("Unknown AP custom field dataType; passing through as string", {
          apFieldId,
          dataType,
        });
        const parts = extractStringsFromCCValue(ccValue);
        return parts.length === 0 ? null : parts[0];
      }
    }
  } catch (error) {
    logError("convertCCToAPValue failed", error);
    return null;
  }
}

export default convertCCToAPValue;

