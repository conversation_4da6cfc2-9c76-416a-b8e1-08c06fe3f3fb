import { getDb, dbSchema } from "@/database";
import { eq } from "drizzle-orm";
import { logDebug, logWarn, logError, logCustomField } from "@/utils/logger";
import apiClient from "@/apiClient";
import type { GetCCCustomField } from "@/type";

// Type for CC custom field value format expected by CC API
export type CCCustomFieldValue = {
  values: Array<{
    id?: number;
    value?: string;
  }>;
  field: {
    id: number;
  };
};

// Type for AP custom field value input
export type APCustomFieldValue = {
  id: string;
  value?: string | number | string[];
  field_value?: Record<string, string>;
};

/**
 * Convert an AP custom field value back to CC format using database-first field resolution.
 *
 * This function:
 * - Uses the AP field ID from the value object to identify the source field
 * - Queries the database to find the corresponding target field mapping
 * - Implements smart API caching strategy (24-hour cache)
 * - Converts the AP value format back to the CC-expected format
 * - Handles TEXTBOX_LIST `field_value` objects by converting them to CC multi-value arrays
 * - Handles AP option values by mapping them back to CC option IDs/values as needed
 * - Returns the value in the format expected by CC's patient custom field update API
 */
export async function convertAPToCCValue(
  apValue: APCustomFieldValue
): Promise<CCCustomFieldValue | null> {
  if (!apValue?.id) {
    logWarn("convertAPToCCValue called without valid AP field ID in value object", {
      hasApValueId: !!apValue?.id,
    });
    return null;
  }

  try {
    const db = getDb();

    // Query database to find the corresponding CC field mapping
    const mappingRows = await db
      .select()
      .from(dbSchema.customFields)
      .where(eq(dbSchema.customFields.apId, apValue.id))
      .limit(1);

    if (mappingRows.length === 0) {
      logWarn("No field mapping found for AP field", {
        apFieldId: apValue.id,
      });
      return null;
    }

    const mapping = mappingRows[0];
    if (!mapping.ccId) {
      logWarn("No CC field ID found in mapping", {
        apFieldId: apValue.id,
        mappingId: mapping.id,
      });
      return null;
    }

    // Smart API caching strategy - check if configuration is fresh (within 24 hours)
    const now = new Date();
    const cacheAge = now.getTime() - new Date(mapping.updatedAt).getTime();
    const cacheMaxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    let ccConfig: GetCCCustomField;

    if (mapping.ccConfig && cacheAge < cacheMaxAge) {
      // Use cached configuration
      ccConfig = mapping.ccConfig as GetCCCustomField;
      logDebug("Using cached CC field configuration", {
        ccFieldId: mapping.ccId,
        cacheAgeHours: Math.round(cacheAge / (60 * 60 * 1000)),
      });
    } else {
      // Fetch fresh configuration from API
      logDebug("Fetching fresh CC field configuration from API", {
        ccFieldId: mapping.ccId,
        cacheAgeHours: cacheAge > 0 ? Math.round(cacheAge / (60 * 60 * 1000)) : 0,
        reason: mapping.ccConfig ? "cache_expired" : "no_cache",
      });

      ccConfig = await apiClient.cc.ccCustomfieldReq.get(mapping.ccId, true);

      // Update the database record with fresh configuration and timestamp
      await db
        .update(dbSchema.customFields)
        .set({
          ccConfig: ccConfig,
          updatedAt: now,
        })
        .where(eq(dbSchema.customFields.id, mapping.id));

      logDebug("Updated database with fresh CC field configuration", {
        ccFieldId: mapping.ccId,
        mappingId: mapping.id,
      });
    }

    logCustomField("AP→CC Value conversion - start", ccConfig.name, {
      apFieldId: apValue.id,
      ccFieldId: mapping.ccId,
      ccFieldType: ccConfig.type,
      ccAllowMultipleValues: ccConfig.allowMultipleValues,
      apValueType: typeof apValue.value,
      hasFieldValue: !!apValue.field_value,
    });

    // Convert AP value to CC format based on the value type and CC field configuration
    const ccValues: Array<{ id?: number; value?: string }> = [];

    // Handle TEXTBOX_LIST field_value (Record<string, string>)
    if (apValue.field_value && typeof apValue.field_value === "object") {
      // Extract values from the field_value object
      const values = Object.values(apValue.field_value).filter(v => v && v.trim() !== "");
      
      if (ccConfig.allowMultipleValues) {
        // For multi-value CC fields, create multiple value entries
        for (const value of values) {
          ccValues.push({ value: String(value).trim() });
        }
      } else {
        // For single-value CC fields, join with " | " separator
        if (values.length > 0) {
          const joinedValue = values.join(" | ");
          ccValues.push({ value: joinedValue });
        }
      }
    }
    // Handle array values (from MULTIPLE_OPTIONS/CHECKBOX)
    else if (Array.isArray(apValue.value)) {
      const values = apValue.value.filter(v => v && String(v).trim() !== "");
      
      if (ccConfig.allowMultipleValues) {
        // For multi-value CC fields, create multiple value entries
        for (const value of values) {
          const stringValue = String(value).trim();
          
          // Try to map back to CC option ID if this is a select field with allowedValues
          if (ccConfig.allowedValues && ccConfig.allowedValues.length > 0) {
            const allowedValue = ccConfig.allowedValues.find(
              av => av.value.toLowerCase() === stringValue.toLowerCase()
            );
            if (allowedValue) {
              ccValues.push({ id: allowedValue.id });
            } else {
              // Value not in allowed values, use as custom value if supported
              ccValues.push({ value: stringValue });
            }
          } else {
            ccValues.push({ value: stringValue });
          }
        }
      } else {
        // For single-value CC fields, use first value or join with " | "
        if (values.length > 0) {
          const firstValue = String(values[0]).trim();
          
          // Try to map back to CC option ID if this is a select field
          if (ccConfig.allowedValues && ccConfig.allowedValues.length > 0) {
            const allowedValue = ccConfig.allowedValues.find(
              av => av.value.toLowerCase() === firstValue.toLowerCase()
            );
            if (allowedValue) {
              ccValues.push({ id: allowedValue.id });
            } else {
              ccValues.push({ value: firstValue });
            }
          } else {
            ccValues.push({ value: firstValue });
          }
        }
      }
    }
    // Handle single value (string/number)
    else if (apValue.value !== undefined && apValue.value !== null) {
      const stringValue = String(apValue.value).trim();
      
      if (stringValue !== "") {
        // Try to map back to CC option ID if this is a select field with allowedValues
        if (ccConfig.allowedValues && ccConfig.allowedValues.length > 0) {
          const allowedValue = ccConfig.allowedValues.find(
            av => av.value.toLowerCase() === stringValue.toLowerCase()
          );
          if (allowedValue) {
            ccValues.push({ id: allowedValue.id });
          } else {
            // Value not in allowed values, use as custom value
            ccValues.push({ value: stringValue });
          }
        } else {
          ccValues.push({ value: stringValue });
        }
      }
    }

    // Return null if no valid values were extracted
    if (ccValues.length === 0) {
      logDebug("No valid values extracted for CC field", {
        apFieldId: apValue.id,
        ccFieldId: mapping.ccId,
      });
      return null;
    }

    const result: CCCustomFieldValue = {
      values: ccValues,
      field: {
        id: ccConfig.id,
      },
    };

    logCustomField("AP→CC Value conversion - complete", ccConfig.name, {
      apFieldId: apValue.id,
      ccFieldId: mapping.ccId,
      resultValuesCount: ccValues.length,
      resultStructure: ccValues.map(v => ({
        hasId: "id" in v,
        hasValue: "value" in v,
      })),
    });

    return result;

  } catch (error) {
    logError("convertAPToCCValue failed", {
      apFieldId: apValue.id,
      error,
    });
    return null;
  }
}

export default convertAPToCCValue;
