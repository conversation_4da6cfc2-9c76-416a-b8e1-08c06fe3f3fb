import { getDb, dbSchema } from "@/database";
import { eq } from "drizzle-orm";
import type { APGetCustomFieldType, GetCCCustomField } from "@/type";

/**
 * Database row type for a custom field mapping record
 * (contains both platform configs: apConfig and ccConfig)
 */
export type CustomFieldMapping = typeof dbSchema.customFields.$inferSelect;

/**
 * Type guard to distinguish AP custom field objects
 */
function isAPCustomField(
  field: APGetCustomFieldType | GetCCCustomField,
): field is APGetCustomFieldType {
  // AP custom fields have string IDs and a dataType property
  return typeof (field as APGetCustomFieldType).id === "string";
}

/**
 * Bidirectional lookup for mapped custom fields.
 *
 * Input can be either an AP custom field or a CC custom field object. The function
 * looks up our `custom_fields` table using the appropriate platform ID and returns
 * the full mapping record (including both apConfig and ccConfig) if found.
 *
 * - If given an AP field, lookup by apId and return the record
 * - If given a CC field, lookup by ccId and return the record
 * - Returns null if no mapping exists
 */
export async function findMappedCustomField(
  field: APGetCustomFieldType | GetCCCustomField,
): Promise<CustomFieldMapping | null> {
  const db = getDb();

  if (isAPCustomField(field)) {
    const rows = await db
      .select()
      .from(dbSchema.customFields)
      .where(eq(dbSchema.customFields.apId, field.id))
      .limit(1);
    return rows[0] ?? null;
  }

  const rows = await db
    .select()
    .from(dbSchema.customFields)
    .where(eq(dbSchema.customFields.ccId, field.id))
    .limit(1);
  return rows[0] ?? null;
}

